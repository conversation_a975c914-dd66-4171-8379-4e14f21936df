<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Plan;
use App\Models\Subscription;
use App\Services\StripeSubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Mockery;

class StripeSubscriptionServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $stripeService;
    protected $user;
    protected $plan;
    protected $subscription;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create([
            'users_id' => 'test-user-id',
            'email' => '<EMAIL>',
            'name' => 'Test User'
        ]);

        // Create test plan
        $this->plan = Plan::factory()->create([
            'plans_id' => 'test-plan-id',
            'name' => 'Test Plan',
            'price' => 29.99,
            'duration' => 'monthly'
        ]);

        // Create test subscription
        $this->subscription = Subscription::create([
            'subscription_id' => 'test-subscription-id',
            'user_id' => $this->user->users_id,
            'plan_id' => $this->plan->plans_id,
            'stripe_subscription_id' => 'sub_test_stripe_id',
            'stripe_customer_id' => 'cus_test_customer_id',
            'status' => 'active',
            'amount' => 29.99,
            'currency' => 'usd',
            'interval' => 'monthly',
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth(),
            'auto_renew' => true,
        ]);

        $this->stripeService = new StripeSubscriptionService();
    }

    /** @test */
    public function it_can_handle_subscription_cancellation_without_stripe_id()
    {
        // Create subscription without Stripe ID
        $subscription = Subscription::create([
            'subscription_id' => 'test-subscription-no-stripe',
            'user_id' => $this->user->users_id,
            'plan_id' => $this->plan->plans_id,
            'stripe_subscription_id' => null,
            'status' => 'active',
            'amount' => 29.99,
            'currency' => 'usd',
            'interval' => 'monthly',
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth(),
            'auto_renew' => true,
        ]);

        $result = $this->stripeService->handleSubscriptionCancellation($subscription, 'user');

        $this->assertTrue($result['success']);
        $this->assertFalse($result['stripe_synced']);
        $this->assertStringContainsString('issue syncing with Stripe', $result['message']);

        // Check that local subscription was updated
        $subscription->refresh();
        $this->assertEquals('cancelled', $subscription->status);
        $this->assertNotNull($subscription->cancelled_at);
        $this->assertNotNull($subscription->ends_at);
    }

    /** @test */
    public function it_updates_local_subscription_status_correctly()
    {
        $result = $this->stripeService->updateLocalSubscriptionStatus($this->subscription, 'cancelled', 'admin');

        $this->assertTrue($result);

        // Check that subscription was updated
        $this->subscription->refresh();
        $this->assertEquals('cancelled', $this->subscription->status);
        $this->assertNotNull($this->subscription->cancelled_at);
        $this->assertNotNull($this->subscription->ends_at);
    }

    /** @test */
    public function it_can_reactivate_subscription_locally()
    {
        // First cancel the subscription
        $this->subscription->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'ends_at' => now()->addMonth()
        ]);

        $result = $this->stripeService->updateLocalSubscriptionStatus($this->subscription, 'active', 'admin');

        $this->assertTrue($result);

        // Check that subscription was reactivated
        $this->subscription->refresh();
        $this->assertEquals('active', $this->subscription->status);
        $this->assertNull($this->subscription->cancelled_at);
        $this->assertNull($this->subscription->ends_at);
    }

    /** @test */
    public function it_checks_stripe_configuration_correctly()
    {
        // Mock config to return empty key
        config(['services.stripe.secret' => '']);
        $this->assertFalse($this->stripeService->isStripeConfigured());

        // Mock config to return default test key
        config(['services.stripe.secret' => 'sk_test_your_stripe_secret_key_here']);
        $this->assertFalse($this->stripeService->isStripeConfigured());

        // Mock config to return valid key
        config(['services.stripe.secret' => 'sk_test_valid_key']);
        $this->assertTrue($this->stripeService->isStripeConfigured());
    }

    /** @test */
    public function it_logs_cancellation_activities()
    {
        Log::shouldReceive('info')
            ->once()
            ->with('Local subscription status updated', Mockery::type('array'));

        $this->stripeService->updateLocalSubscriptionStatus($this->subscription, 'cancelled', 'user');
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
