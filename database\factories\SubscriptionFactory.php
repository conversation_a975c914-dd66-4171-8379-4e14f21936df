<?php

namespace Database\Factories;

use App\Models\Subscription;
use App\Models\User;
use App\Models\Plan;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class SubscriptionFactory extends Factory
{
    protected $model = Subscription::class;

    public function definition()
    {
        return [
            'subscription_id' => (string) Str::uuid(),
            'user_id' => User::factory(),
            'plan_id' => Plan::factory(),
            'stripe_subscription_id' => 'sub_' . Str::random(24),
            'stripe_customer_id' => 'cus_' . Str::random(24),
            'status' => 'active',
            'amount' => $this->faker->randomFloat(2, 9.99, 99.99),
            'currency' => 'usd',
            'interval' => $this->faker->randomElement(['monthly', 'yearly']),
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth(),
            'trial_ends_at' => null,
            'cancelled_at' => null,
            'ends_at' => null,
            'auto_renew' => true,
            'metadata' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'active',
                'cancelled_at' => null,
                'ends_at' => null,
            ];
        });
    }

    public function cancelled()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'ends_at' => now()->addMonth(),
            ];
        });
    }

    public function expired()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'expired',
                'ends_at' => now()->subDay(),
            ];
        });
    }

    public function monthly()
    {
        return $this->state(function (array $attributes) {
            return [
                'interval' => 'monthly',
                'current_period_end' => now()->addMonth(),
            ];
        });
    }

    public function yearly()
    {
        return $this->state(function (array $attributes) {
            return [
                'interval' => 'yearly',
                'current_period_end' => now()->addYear(),
            ];
        });
    }

    public function withoutStripe()
    {
        return $this->state(function (array $attributes) {
            return [
                'stripe_subscription_id' => null,
                'stripe_customer_id' => null,
            ];
        });
    }
}
