<?php

namespace App\Services;

use App\Models\Subscription;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\Exception\ApiErrorException;

class StripeSubscriptionService
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Cancel a subscription in Stripe
     *
     * @param Subscription $subscription
     * @param string $cancelledBy - 'user' or 'admin'
     * @return array
     */
    public function cancelSubscription(Subscription $subscription, string $cancelledBy = 'user'): array
    {
        if (!$subscription->stripe_subscription_id) {
            return [
                'success' => true,
                'message' => 'No Stripe subscription to cancel',
                'stripe_synced' => false
            ];
        }

        try {
            $stripeSubscription = \Stripe\Subscription::retrieve($subscription->stripe_subscription_id);
            $stripeSubscription->cancel();

            Log::info('Stripe subscription cancelled', [
                'subscription_id' => $subscription->subscription_id,
                'stripe_subscription_id' => $subscription->stripe_subscription_id,
                'cancelled_by' => $cancelledBy,
                'user_id' => $subscription->user_id
            ]);

            return [
                'success' => true,
                'message' => 'Subscription cancelled in Stripe successfully',
                'stripe_synced' => true
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe subscription cancellation failed', [
                'subscription_id' => $subscription->subscription_id,
                'stripe_subscription_id' => $subscription->stripe_subscription_id,
                'cancelled_by' => $cancelledBy,
                'user_id' => $subscription->user_id,
                'error' => $e->getMessage(),
                'error_code' => $e->getStripeCode()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to cancel subscription in Stripe: ' . $e->getMessage(),
                'stripe_synced' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update local subscription status after Stripe operation
     *
     * @param Subscription $subscription
     * @param string $status
     * @param string $cancelledBy
     * @return bool
     */
    public function updateLocalSubscriptionStatus(Subscription $subscription, string $status, string $cancelledBy = 'user'): bool
    {
        try {
            $updateData = [
                'status' => $status,
            ];

            if ($status === 'cancelled') {
                $updateData['cancelled_at'] = now();
                $updateData['ends_at'] = $subscription->current_period_end;
            } elseif ($status === 'active') {
                $updateData['cancelled_at'] = null;
                $updateData['ends_at'] = null;
            }

            $subscription->update($updateData);

            Log::info('Local subscription status updated', [
                'subscription_id' => $subscription->subscription_id,
                'new_status' => $status,
                'updated_by' => $cancelledBy,
                'user_id' => $subscription->user_id
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to update local subscription status', [
                'subscription_id' => $subscription->subscription_id,
                'new_status' => $status,
                'updated_by' => $cancelledBy,
                'user_id' => $subscription->user_id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Handle complete subscription cancellation (Stripe + Local)
     *
     * @param Subscription $subscription
     * @param string $cancelledBy
     * @return array
     */
    public function handleSubscriptionCancellation(Subscription $subscription, string $cancelledBy = 'user'): array
    {
        // First, try to cancel in Stripe
        $stripeResult = $this->cancelSubscription($subscription, $cancelledBy);
        
        // Update local status regardless of Stripe result
        $localUpdateSuccess = $this->updateLocalSubscriptionStatus($subscription, 'cancelled', $cancelledBy);

        if (!$localUpdateSuccess) {
            return [
                'success' => false,
                'message' => 'Failed to update local subscription status',
                'stripe_synced' => $stripeResult['stripe_synced']
            ];
        }

        $message = 'Subscription cancelled successfully.';
        
        if (!$stripeResult['stripe_synced']) {
            $message .= ' Note: There was an issue syncing with Stripe payment system.';
        }

        return [
            'success' => true,
            'message' => $message,
            'stripe_synced' => $stripeResult['stripe_synced'],
            'stripe_error' => $stripeResult['error'] ?? null
        ];
    }

    /**
     * Check if Stripe is properly configured
     *
     * @return bool
     */
    public function isStripeConfigured(): bool
    {
        $secretKey = config('services.stripe.secret');
        return !empty($secretKey) && $secretKey !== 'sk_test_your_stripe_secret_key_here';
    }

    /**
     * Get subscription status from Stripe
     *
     * @param string $stripeSubscriptionId
     * @return array|null
     */
    public function getStripeSubscriptionStatus(string $stripeSubscriptionId): ?array
    {
        try {
            $stripeSubscription = \Stripe\Subscription::retrieve($stripeSubscriptionId);
            
            return [
                'id' => $stripeSubscription->id,
                'status' => $stripeSubscription->status,
                'current_period_start' => $stripeSubscription->current_period_start,
                'current_period_end' => $stripeSubscription->current_period_end,
                'cancel_at_period_end' => $stripeSubscription->cancel_at_period_end,
                'canceled_at' => $stripeSubscription->canceled_at,
            ];

        } catch (ApiErrorException $e) {
            Log::error('Failed to retrieve Stripe subscription status', [
                'stripe_subscription_id' => $stripeSubscriptionId,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }
}
