@extends('layouts.app')

@section('content')
@include('layouts.navbars.auth.topnav', ['title' => 'Subscriptions'])

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-12 col-md-12">
            <div class="card mb-4">
                <div class="d-flex justify-content-between px-3 py-4">
                    <h6 class="mb-0" style="color: #67748e;">User Subscriptions</h6>
                    <a href="{{ route('admin.payments.index') }}" class="btn btn-sm m-0" style="background-color: #67748e; border-color: #67748e; color: white;">
                        <i class="fas fa-credit-card me-1"></i> View Payments
                    </a>
                </div>

                <div class="card-body p-3">
                    <x-data-table id="subscriptionsTable" :ajax="route('admin.subscriptions.index')" :columns="[
                        [
                            'data' => 'DT_RowIndex',
                            'name' => 'DT_RowIndex',
                            'orderable' => false,
                            'searchable' => false,
                        ],
                        ['data' => 'user_info', 'name' => 'user.name'],
                        ['data' => 'plan_info', 'name' => 'plan.name'],
                        ['data' => 'status_badge', 'name' => 'status'],
                        ['data' => 'period_info', 'name' => 'current_period_start', 'orderable' => false],
                        ['data' => 'auto_renew_status', 'name' => 'auto_renew', 'orderable' => false],
                        ['data' => 'created_at', 'name' => 'created_at'],
                        ['data' => 'actions', 'name' => 'actions', 'orderable' => false, 'searchable' => false],
                    ]" :order="[[6, 'desc']]">
                        <x-slot:header>
                            <th>S.No.</th>
                            <th>User</th>
                            <th>Plan</th>
                            <th>Status</th>
                            <th>Period</th>
                            <th>Auto Renew</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </x-slot:header>
                    </x-data-table>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function toggleStatus(subscriptionId) {
    Swal.fire({
        title: 'Toggle Subscription Status?',
        text: "This will change the subscription status and sync with Stripe payment system.",
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, toggle it!',
        cancelButtonText: 'Cancel',
        scrollbarPadding: false
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading state
            Swal.fire({
                title: 'Processing...',
                text: 'Updating subscription status and syncing with Stripe...',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: `/admin/subscriptions/${subscriptionId}/toggle-status`,
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        // Reload the DataTable
                        $('.data-table').DataTable().ajax.reload();

                        // Show success toast with Stripe sync status
                        let toastIcon = 'success';
                        let toastTitle = 'Status Updated!';
                        let toastText = response.message;

                        if (!response.stripe_synced) {
                            toastIcon = 'warning';
                            toastTitle = 'Partially Updated';
                        }

                        // Show toast notification (preferred by user)
                        const Toast = Swal.mixin({
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 5000,
                            timerProgressBar: true,
                            didOpen: (toast) => {
                                toast.addEventListener('mouseenter', Swal.stopTimer)
                                toast.addEventListener('mouseleave', Swal.resumeTimer)
                            }
                        });

                        Toast.fire({
                            icon: toastIcon,
                            title: toastTitle,
                            text: toastText
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: response.message || 'Failed to update subscription status.',
                            icon: 'error',
                            timer: 3000,
                            showConfirmButton: false,
                            scrollbarPadding: false
                        });
                    }
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON?.message || 'An error occurred while updating the subscription status.';
                    Swal.fire({
                        title: 'Error!',
                        text: errorMsg,
                        icon: 'error',
                        timer: 3000,
                        showConfirmButton: false,
                        scrollbarPadding: false
                    });
                }
            });
        }
    });
}
</script>
@endsection