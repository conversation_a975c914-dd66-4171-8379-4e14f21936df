<?php

namespace Database\Factories;

use App\Models\Plan;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class PlanFactory extends Factory
{
    protected $model = Plan::class;

    public function definition()
    {
        return [
            'plans_id' => (string) Str::uuid(),
            'name' => $this->faker->words(2, true) . ' Plan',
            'description' => $this->faker->sentence(),
            'price' => $this->faker->randomFloat(2, 9.99, 99.99),
            'duration' => $this->faker->randomElement(['monthly', 'yearly']),
            'status' => 1,
            'is_popular' => 0,
            'sort_order' => $this->faker->numberBetween(0, 100),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 1,
            ];
        });
    }

    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 0,
            ];
        });
    }

    public function monthly()
    {
        return $this->state(function (array $attributes) {
            return [
                'duration' => 'monthly',
            ];
        });
    }

    public function yearly()
    {
        return $this->state(function (array $attributes) {
            return [
                'duration' => 'yearly',
            ];
        });
    }
}
