// Profile Page JavaScript

document.addEventListener('DOMContentLoaded', function () {
    // Get password form elements
    const passwordDisplayEl = document.getElementById('passwordDisplay');
    const passwordChangeFormEl = document.getElementById('passwordChangeForm');

    // Check if password form should be shown on page load (due to validation errors)
    if (passwordChangeFormEl && passwordChangeFormEl.style.display === 'block') {
        if (passwordDisplayEl) {
            passwordDisplayEl.style.display = 'none';
        }
    }
    // Profile form handling (includes password if changed)
    const profileForm = document.getElementById('profile-form');
    if (profileForm) {
        profileForm.addEventListener('submit', function (e) {
            const fullName = document.getElementById('fullName').value;
            const passwordChangeForm = document.getElementById('passwordChangeForm');
            const isPasswordChangeVisible = passwordChangeForm && passwordChangeForm.style.display !== 'none';

            // Basic validation
            if (!fullName.trim()) {
                e.preventDefault();
                showProfileMessage('Please enter your full name.', 'error');
                return;
            }

            // Password validation if password change form is visible
            if (isPasswordChangeVisible) {
                const currentPassword = document.getElementById('currentPassword').value;
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;

                if (!currentPassword || !newPassword || !confirmPassword) {
                    e.preventDefault();
                    showProfileMessage('Please fill in all password fields or cancel password change.', 'error');
                    return;
                }

                if (newPassword.length < 8) {
                    e.preventDefault();
                    showProfileMessage('New password must be at least 8 characters long.', 'error');
                    return;
                }

                if (newPassword !== confirmPassword) {
                    e.preventDefault();
                    showProfileMessage('New passwords do not match.', 'error');
                    return;
                }
            }

            // Show loading state
            const submitBtn = this.querySelector('#updateProfileBtn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';

            // Form will submit naturally to server
        });
    }

    // Password Toggle View Button
    const togglePasswordView = document.getElementById('togglePasswordView');
    if (togglePasswordView) {
        togglePasswordView.addEventListener('click', function () {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }

    // Change Password Button
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const passwordDisplay = document.getElementById('passwordDisplay');
    const passwordChangeForm = document.getElementById('passwordChangeForm');

    if (changePasswordBtn) {
        changePasswordBtn.addEventListener('click', function () {
            passwordDisplay.style.display = 'none';
            passwordChangeForm.style.display = 'block';

            // Focus on current password field
            const currentPasswordInput = document.getElementById('currentPassword');
            if (currentPasswordInput) {
                currentPasswordInput.focus();
            }
        });
    }



    // Cancel button handling
    const cancelBtn = document.getElementById('cancelBtn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function () {
            // Reset form to original values by reloading the page
            window.location.reload();
        });
    }

    // Password toggle functionality for profile form
    const passwordToggles = document.querySelectorAll('.password-toggle');
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', function () {
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });

    // Subscription action buttons
    // const changePlanBtn = document.querySelector('.subscription-actions .btn-secondary');
    // if (changePlanBtn) {
    //     changePlanBtn.addEventListener('click', function () {
    //         showProfileMessage('Redirecting to pricing page...', 'info');
    //         setTimeout(() => {
    //             window.location.href = 'pricing.html';
    //         }, 1000);
    //     });
    // }

    const cancelSubscriptionBtn = document.querySelector('.subscription-actions .btn-danger');
    if (cancelSubscriptionBtn) {
        cancelSubscriptionBtn.addEventListener('click', function () {
            if (confirm('Are you sure you want to cancel your subscription? This action cannot be undone.')) {
                showProfileMessage('Processing cancellation request...', 'info');

                setTimeout(() => {
                    showProfileMessage('Subscription cancellation request submitted. You will receive a confirmation email shortly.', 'success');
                }, 2000);
            }
        });
    }

    // Profile Image Upload Functionality
    const profileImage = document.getElementById('profileImage');
    const imageUpload = document.getElementById('imageUpload');
    const profileImageWrapper = document.querySelector('.profile-image-wrapper');

    // Set default image if none exists
    if (profileImage && !profileImage.src.includes('default-avatar')) {
        // Check if user has a custom image, otherwise show default
        profileImage.onerror = function () {
            this.src = 'images/default-avatar.svg';
        };
    }

    // Profile image wrapper click handler
    if (profileImageWrapper && imageUpload) {
        profileImageWrapper.addEventListener('click', function () {
            imageUpload.click();
        });
    }

    // File input change handler
    if (imageUpload && profileImage) {
        imageUpload.addEventListener('change', function (e) {
            const file = e.target.files[0];
            if (file) {
                // Validate file type
                if (!file.type.startsWith('image/')) {
                    showProfileMessage('Please select a valid image file.', 'error');
                    return;
                }

                // Validate file size (max 5MB)
                if (file.size > 5 * 1024 * 1024) {
                    showProfileMessage('Image size must be less than 5MB.', 'error');
                    return;
                }

                // Read and display the image
                const reader = new FileReader();
                reader.onload = function (e) {
                    profileImage.src = e.target.result;
                    profileImage.dataset.hasCustomImage = 'true';

                    // Show success message
                    showProfileMessage('Profile image updated successfully! Right-click to remove.', 'success');

                    // Mark form as having changes
                    markFormAsChanged();
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Right-click context menu for removing image
    if (profileImageWrapper && profileImage) {
        profileImageWrapper.addEventListener('contextmenu', function (e) {
            // Only show context menu if user has uploaded a custom image
            if (profileImage.dataset.hasCustomImage === 'true') {
                e.preventDefault();

                if (confirm('Do you want to remove your profile picture?')) {
                    // Reset to default avatar
                    profileImage.src = 'images/default-avatar.svg';
                    profileImage.dataset.hasCustomImage = 'false';

                    // Clear file input
                    if (imageUpload) {
                        imageUpload.value = '';
                    }

                    showProfileMessage('Profile image removed.', 'info');
                    markFormAsChanged();
                }
            }
        });
    }

    // Auto-save functionality (optional)
    const formInputs = document.querySelectorAll('#profile-form input');
    formInputs.forEach(input => {
        if (input.type !== 'password' && input.type !== 'file') {
            input.addEventListener('blur', function () {
                markFormAsChanged();
            });
        }
    });
});

// Utility functions
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showProfileMessage(message, type) {
    // Remove existing messages
    const existingMessage = document.querySelector('.profile-message');
    if (existingMessage) {
        existingMessage.remove();
    }

    // Create new message
    const messageDiv = document.createElement('div');
    messageDiv.className = `profile-message profile-message-${type}`;
    messageDiv.innerHTML = `
        <div class="message-content">
            <i class="fas ${getMessageIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;

    // Insert at the top of the profile section
    const profileSection = document.querySelector('.profile-section .container');
    if (profileSection) {
        profileSection.insertBefore(messageDiv, profileSection.firstChild);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    messageDiv.remove();
                }, 300);
            }
        }, 5000);
    }
}

function getMessageIcon(type) {
    switch (type) {
        case 'success': return 'fa-check-circle';
        case 'error': return 'fa-exclamation-circle';
        case 'info': return 'fa-info-circle';
        default: return 'fa-info-circle';
    }
}

// Add some interactive features
document.addEventListener('DOMContentLoaded', function () {
    // Add hover effects to plan features
    const planFeatures = document.querySelectorAll('.plan-features li');
    planFeatures.forEach(feature => {
        feature.addEventListener('mouseenter', function () {
            this.style.background = 'rgba(59, 130, 246, 0.1)';
            this.style.padding = '8px 10px';
            this.style.borderRadius = '5px';
            this.style.transition = 'all 0.3s ease';
        });

        feature.addEventListener('mouseleave', function () {
            this.style.background = 'transparent';
            this.style.padding = '8px 0';
        });
    });

    // Add click to copy functionality for payment method
    const paymentMethod = document.querySelector('.detail-item .value');
    if (paymentMethod && paymentMethod.textContent.includes('****')) {
        paymentMethod.style.cursor = 'pointer';
        paymentMethod.title = 'Click to view full card number';

        paymentMethod.addEventListener('click', function () {
            if (this.textContent.includes('****')) {
                this.textContent = '4532 1234 5678 1234';
                this.style.color = '#1e3a8a';
                setTimeout(() => {
                    this.textContent = '**** **** **** 1234';
                    this.style.color = '#6b7280';
                }, 3000);
            }
        });
    }
});

// Form validation helpers
function validateMobileNumber(mobile) {
    const mobileRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return mobileRegex.test(mobile.replace(/[\s\-\(\)]/g, ''));
}

function formatMobileNumber(mobile) {
    // Simple US phone number formatting
    const cleaned = mobile.replace(/\D/g, '');
    if (cleaned.length === 10) {
        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    } else if (cleaned.length === 11 && cleaned[0] === '1') {
        return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
    }
    return mobile;
}

// Auto-format mobile number on input
document.addEventListener('DOMContentLoaded', function () {
    const mobileInput = document.getElementById('mobile');
    if (mobileInput) {
        mobileInput.addEventListener('blur', function () {
            this.value = formatMobileNumber(this.value);
        });
    }
});

// Reset password form function
function resetPasswordForm() {
    const passwordDisplay = document.getElementById('passwordDisplay');
    const passwordChangeForm = document.getElementById('passwordChangeForm');

    if (passwordDisplay && passwordChangeForm) {
        // Show password display, hide change form
        passwordDisplay.style.display = 'block';
        passwordChangeForm.style.display = 'none';

        // Clear password fields
        document.getElementById('currentPassword').value = '';
        document.getElementById('newPassword').value = '';
        document.getElementById('confirmPassword').value = '';
    }
}

// Mark form as changed function
function markFormAsChanged() {
    const submitBtn = document.querySelector('#profile-form button[type="submit"]');
    if (submitBtn && !submitBtn.classList.contains('has-changes')) {
        submitBtn.classList.add('has-changes');
        submitBtn.style.background = 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)';
    }
}

// Image compression function (optional)
function compressImage(file, maxWidth = 300, quality = 0.8) {
    return new Promise((resolve) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = function () {
            const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
            canvas.width = img.width * ratio;
            canvas.height = img.height * ratio;

            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

            canvas.toBlob(resolve, 'image/jpeg', quality);
        };

        img.src = URL.createObjectURL(file);
    });
}
