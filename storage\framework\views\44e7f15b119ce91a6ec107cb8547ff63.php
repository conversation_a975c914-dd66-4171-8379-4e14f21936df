

<?php $__env->startPush('css'); ?>
<style>
    .alert {
        padding: 0.75rem 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid transparent;
        border-radius: 0.375rem;
        position: relative;
    }

    .alert-success {
        color: #0f5132;
        background-color: #d1e7dd;
        border-color: #badbcc;
    }

    .alert-danger {
        color: #842029;
        background-color: #f8d7da;
        border-color: #f5c2c7;
    }

    .alert-dismissible .btn-close {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 2;
        padding: 0.9375rem 1.25rem;
        background: none;
        border: none;
        font-size: 1.125rem;
        cursor: pointer;
    }

    .text-danger {
        color: #dc3545 !important;
    }

    .fade {
        transition: opacity 0.15s linear;
    }

    .show {
        opacity: 1;
    }

    /* No Subscription Styles */
    .no-subscription {
        text-align: center;
        padding: 60px 30px;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 12px;
        border: 2px dashed #cbd5e1;
    }

    .no-subscription-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        color: #64748b;
        font-size: 2rem;
    }

    .no-subscription h3 {
        color: #475569;
        font-size: 1.5rem;
        margin-bottom: 15px;
    }

    .no-subscription p {
        color: #64748b;
        font-size: 1rem;
        margin-bottom: 30px;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    .status-inactive {
        color: #ef4444 !important;
        font-weight: 600;
    }

    .plan-status.cancelled {
        background: #fef2f2;
        color: #dc2626;
    }

    .plan-status.expired {
        background: #fefce8;
        color: #ca8a04;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <section class="profile-section">
        <div class="container">
            <div class="profile-header">
                <h1>My Profile</h1>
                <p>Manage your account settings and subscription</p>
            </div>

            <div class="profile-container">
                <!-- Profile Information Section -->
                <div class="profile-card">
                    <div class="card-header">
                        <h2><i class="fas fa-user"></i> Profile Information</h2>
                    </div>

                    <form id="profile-form" class="profile-form" method="POST" action="<?php echo e(route('user.profile.update')); ?>" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <!-- Profile Image Section -->
                        <div class="profile-image-section">
                            <div class="profile-image-container">
                                <div class="profile-image-wrapper">
                                    <img src="<?php echo e($user->image ? asset('storage/' . $user->image) : asset('assets/images/logo.png')); ?>"
                                         alt="Profile Picture" id="profileImage" class="profile-image">
                                    <div class="image-overlay">
                                        <i class="fas fa-camera"></i>
                                        <span>Click to Upload</span>
                                    </div>
                                </div>
                                <input type="file" id="imageUpload" name="image" accept="image/*" style="display: none;">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="fullName">Full Name *</label>
                                <div class="input-group">
                                    <i class="fas fa-user input-icon"></i>
                                    <input type="text" id="fullName" name="name" value="<?php echo e(old('name', $user->name)); ?>" required>
                                </div>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <small class="text-danger"><?php echo e($message); ?></small>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="form-group">
                                <label for="email">Email Address *</label>
                                <div class="input-group">
                                    <i class="fas fa-envelope input-icon"></i>
                                    <input type="email" id="email" name="email" value="<?php echo e(old('email', $user->email)); ?>" required>
                                </div>
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <small class="text-danger"><?php echo e($message); ?></small>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="mobile">Mobile Number</label>
                            <div class="input-group">
                                <i class="fas fa-phone input-icon"></i>
                                <input type="tel" id="mobile" name="mobile" value="<?php echo e(old('mobile', $user->mobile)); ?>">
                            </div>
                            <?php if(session('error') && str_contains(session('error'), 'Mobile')): ?>
                                <small class="text-danger">
                                    <?php if(str_contains(session('error'), 'at least 10')): ?>
                                        Mobile number must be at least 10 digits long.
                                    <?php elseif(str_contains(session('error'), 'only contain')): ?>
                                        Mobile number can only contain numbers, spaces, +, -, ( and ) characters.
                                    <?php else: ?>
                                        <?php echo e(session('error')); ?>

                                    <?php endif; ?>
                                </small>
                            <?php endif; ?>
                        </div>

                        <!-- Password Section -->
                        <div class="password-section">
                            <!-- Default Password Display -->
                            <div class="password-display" id="passwordDisplay" style="display: <?php echo e(session('show_password_form') ? 'none' : 'block'); ?>;">
                                <div class="form-group">
                                    <label for="password">Password</label>
                                    <div class="input-group">
                                        <i class="fas fa-lock input-icon"></i>
                                        <input type="password" id="password" name="password" value="••••••••••••"
                                            readonly>
                                        <button type="button" class="password-toggle-view" id="togglePasswordView">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="password-action">
                                    <button type="button" class="btn btn-secondary change-password-btn"
                                        id="changePasswordBtn">
                                        <i class="fas fa-edit"></i>
                                        Change Password
                                    </button>
                                </div>
                            </div>

                            <!-- Password Change Form (Hidden by default) -->
                            <div class="password-change-form" id="passwordChangeForm" style="display: <?php echo e(session('show_password_form') ? 'block' : 'none'); ?>;">
                                <div class="form-group">
                                    <label for="currentPassword">Current Password *</label>
                                    <div class="input-group">
                                        <i class="fas fa-lock input-icon"></i>
                                        <input type="password" id="currentPassword" name="current_password"
                                            placeholder="Enter current password">
                                        <button type="button" class="password-toggle" id="toggleCurrentPassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <?php if(session('error') && str_contains(session('error'), 'Current password')): ?>
                                        <small class="text-danger">Current password is incorrect.</small>
                                    <?php endif; ?>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="newPassword">New Password *</label>
                                        <div class="input-group">
                                            <i class="fas fa-lock input-icon"></i>
                                            <input type="password" id="newPassword" name="new_password"
                                                placeholder="Enter new password">
                                            <button type="button" class="password-toggle" id="toggleNewPassword">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <?php if(session('error') && (str_contains(session('error'), 'password') && !str_contains(session('error'), 'Current password'))): ?>
                                            <small class="text-danger">
                                                <?php if(str_contains(session('error'), 'confirmation')): ?>
                                                    New password confirmation does not match.
                                                <?php elseif(str_contains(session('error'), 'at least')): ?>
                                                    Password must be at least 8 characters with mixed case, letters, numbers and symbols.
                                                <?php else: ?>
                                                    <?php echo e(session('error')); ?>

                                                <?php endif; ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>

                                    <div class="form-group">
                                        <label for="confirmPassword">Confirm New Password *</label>
                                        <div class="input-group">
                                            <i class="fas fa-lock input-icon"></i>
                                            <input type="password" id="confirmPassword" name="new_password_confirmation"
                                                placeholder="Confirm new password">
                                            <button type="button" class="password-toggle" id="toggleConfirmPassword">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary" id="updateProfileBtn">
                                <i class="fas fa-save"></i>
                                Update Profile
                            </button>
                            <button type="button" class="btn btn-secondary" id="cancelBtn">
                                <i class="fas fa-times"></i>
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Subscription Section -->
                <div class="subscription-card">
                    <div class="card-header">
                        <h2><i class="fas fa-credit-card"></i> Subscription Details</h2>
                    </div>

                    <div class="subscription-info">
                        <?php if($activeSubscription): ?>
                            <!-- Current Plan -->
                            <div class="current-plan">
                                <?php if($activeSubscription->plan->is_popular): ?>
                                    <div class="plan-badge popular">
                                        <i class="fas fa-star"></i>
                                        Most Popular
                                    </div>
                                <?php endif; ?>

                                <div class="plan-header">
                                    <h3><?php echo e($activeSubscription->plan->name); ?></h3>
                                    <div class="plan-price">
                                        <span class="price">$<?php echo e(number_format($activeSubscription->plan->price, 2)); ?></span>
                                        <span class="duration">/<?php echo e($activeSubscription->plan->duration === 'yearly' ? 'year' : ($activeSubscription->plan->duration === 'monthly' ? 'month' : 'week')); ?></span>
                                    </div>
                                </div>

                                <div class="plan-status <?php echo e(strtolower($activeSubscription->status)); ?>">
                                    <i class="fas fa-<?php echo e($activeSubscription->status === 'active' ? 'check-circle' : ($activeSubscription->status === 'cancelled' ? 'times-circle' : 'clock')); ?>"></i>
                                    <?php echo e(ucfirst($activeSubscription->status)); ?>

                                </div>

                            <!-- Features and Details Row -->
                            <div class="features-details-row">
                                <div class="plan-features">
                                    <h4>Plan Features:</h4>
                                    <ul>
                                        <?php if($activeSubscription->plan->features && count($activeSubscription->plan->features) > 0): ?>
                                            <?php $__currentLoopData = $activeSubscription->plan->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <li><i class="fas fa-check"></i> <?php echo e($feature); ?></li>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php else: ?>
                                            <li><i class="fas fa-check"></i> All basic features included</li>
                                        <?php endif; ?>
                                    </ul>
                                </div>

                                <!-- Subscription Details -->
                                <div class="subscription-details">
                                    <h4>Billing Details:</h4>
                                    <div class="detail-item">
                                        <span class="label">Next Billing Date:</span>
                                        <span class="value">
                                            <?php if($activeSubscription->current_period_end): ?>
                                                <?php echo e($activeSubscription->current_period_end->format('F j, Y')); ?>

                                            <?php else: ?>
                                                Not available
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="label">Subscription ID:</span>
                                        <span class="value"><?php echo e(substr($activeSubscription->subscription_id, 0, 8)); ?>...</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="label">Auto Renewal:</span>
                                        <span class="value <?php echo e($activeSubscription->auto_renew ? 'status-active' : 'status-inactive'); ?>">
                                            <?php echo e($activeSubscription->auto_renew ? 'Enabled' : 'Disabled'); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="plan-description">
                                <p><?php echo e($activeSubscription->plan->description ?? 'Enjoy all the features of your current subscription plan.'); ?></p>
                            </div>

                            <div class="subscription-actions">
                                <a href="<?php echo e(route('user.subscriptions.index')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-eye"></i>
                                    View Details
                                </a>
                                <a href="<?php echo e(route('pricing')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-edit"></i>
                                    Browse Plans
                                </a>
                            </div>
                        </div>
                        <?php else: ?>
                        <!-- No Active Subscription -->
                        <div class="no-subscription">
                            <div class="no-subscription-icon">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <h3>No Active Subscription</h3>
                            <p>You don't have any active subscription plan. Choose a plan to get started with our services.</p>

                            <div class="subscription-actions">
                                <a href="<?php echo e(route('pricing')); ?>" class="btn btn-primary">
                                    <i class="fas fa-plus"></i>
                                    Choose a Plan
                                </a>
                                <?php if($subscriptionHistory && $subscriptionHistory->count() > 0): ?>
                                    <a href="<?php echo e(route('user.subscriptions.index')); ?>" class="btn btn-outline-secondary">
                                        <i class="fas fa-history"></i>
                                        View History
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="<?php echo e(asset('assets/js/profile.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/user/profile.blade.php ENDPATH**/ ?>